import React from "react";

interface Step {
  number: number;
  title: string;
  description: string;
}

const steps: Step[] = [
  {
    number: 1,
    title: "Repository Analysis",
    description: "We fetch and analyze the code structure from your GitHub repository",
  },
  {
    number: 2,
    title: "Concept Identification",
    description: "Our AI identifies core abstractions and relationships",
  },
  {
    number: 3,
    title: "Content Generation",
    description: "Detailed explanations, examples, and diagrams are created",
  },
  {
    number: 4,
    title: "Tutorial Assembly",
    description: "Everything is compiled into a complete, navigable tutorial",
  },
];

export const HowItWorks: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold mb-4 text-gray-800">
        How It Works
      </h2>

      <div className="space-y-4">
        {steps.map((step) => (
          <div key={step.number} className="flex">
            <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-tutorial-primary mr-3">
              {step.number}
            </div>
            <div>
              <h3 className="font-medium text-gray-800 mb-1">
                {step.title}
              </h3>
              <p className="text-sm text-gray-600">
                {step.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
