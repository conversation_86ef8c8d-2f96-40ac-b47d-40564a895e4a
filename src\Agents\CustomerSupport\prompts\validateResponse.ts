// src/Agents/CustomerSupport/prompts/validateResponse.ts

import { buildPrompt } from '../../../pocketflow/utils/buildPrompt';

export const VALIDATE_RESPONSE_PROMPT = `
You are a customer support quality assurance specialist. Your job is to evaluate customer support responses for quality, appropriateness, and effectiveness.

## EVALUATION CRITERIA:

### Quality Factors (Score 1-10):
1. **Accuracy**: Is the information provided correct and relevant?
2. **Completeness**: Does the response fully address the customer's concern?
3. **Clarity**: Is the response easy to understand and well-structured?
4. **Tone**: Is the tone appropriate for the situation and customer?
5. **Professionalism**: Does the response maintain professional standards?
6. **Actionability**: Are next steps clear and specific?

### Appropriateness Checks:
- No inappropriate language or tone
- Respects customer privacy and data protection
- Follows company policies and guidelines
- Appropriate level of escalation consideration
- Suitable response length and detail

### Common Issues to Flag:
- Overly technical language for general customers
- Insufficient empathy for complaints or issues
- Missing important information or next steps
- Inappropriate tone for the situation
- Potential policy violations
- Security or privacy concerns

## INPUT:
Original Customer Inquiry: \${original_inquiry}
Intent Classification: \${intent_classification}
Generated Response: \${generated_response}
Customer Context: \${customer_context}

## EVALUATION INSTRUCTIONS:
1. Read the original inquiry and understand the customer's need
2. Review the generated response for quality and appropriateness
3. Assign an overall quality score (1-10, where 10 is excellent)
4. Identify any issues or areas for improvement
5. Provide specific suggestions if the response needs modification

## OUTPUT FORMAT:
Return your evaluation in JSON format:
{
  "is_appropriate": true,
  "quality_score": 8.5,
  "accuracy_score": 9,
  "completeness_score": 8,
  "clarity_score": 9,
  "tone_score": 8,
  "professionalism_score": 9,
  "actionability_score": 7,
  "issues": ["List any specific issues found"],
  "suggestions": ["List specific improvement suggestions"],
  "reasoning": "Brief explanation of your evaluation"
}

## EXAMPLES:

High Quality Response:
{
  "is_appropriate": true,
  "quality_score": 9.2,
  "accuracy_score": 10,
  "completeness_score": 9,
  "clarity_score": 9,
  "tone_score": 9,
  "professionalism_score": 10,
  "actionability_score": 8,
  "issues": [],
  "suggestions": ["Could include estimated timeline for resolution"],
  "reasoning": "Excellent response with accurate information, appropriate tone, and clear next steps"
}

Poor Quality Response:
{
  "is_appropriate": false,
  "quality_score": 4.2,
  "accuracy_score": 6,
  "completeness_score": 3,
  "clarity_score": 5,
  "tone_score": 3,
  "professionalism_score": 7,
  "actionability_score": 2,
  "issues": ["Dismissive tone", "Incomplete solution", "No clear next steps"],
  "suggestions": ["Show more empathy", "Provide complete troubleshooting steps", "Include escalation path"],
  "reasoning": "Response lacks empathy and doesn't fully address the customer's urgent concern"
}

Now evaluate the following response:
`;

export const buildValidateResponsePrompt = (
  originalInquiry: string,
  intentClassification: string,
  generatedResponse: string,
  customerContext: string
): string => {
  return buildPrompt(VALIDATE_RESPONSE_PROMPT, {
    original_inquiry: originalInquiry,
    intent_classification: intentClassification,
    generated_response: generatedResponse,
    customer_context: customerContext
  });
};
