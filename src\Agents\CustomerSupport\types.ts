// src/Agents/CustomerSupport/types.ts

/**
 * Shared store used across the CustomerSupport PocketFlow workflow
 */

export interface CustomerInquiry {
  id: string;
  message: string;
  timestamp: Date;
  channel: 'email' | 'chat' | 'phone' | 'web';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  customer_id?: string;
}

export interface CustomerContext {
  customer_id?: string;
  name?: string;
  email?: string;
  phone?: string;
  subscription_tier?: string;
  account_status?: string;
  previous_tickets?: string[];
  purchase_history?: any[];
}

export interface IntentClassification {
  intent: 'billing' | 'technical' | 'general' | 'complaint' | 'feature_request' | 'cancellation';
  confidence: number;
  sub_category?: string;
  urgency_level: 'low' | 'medium' | 'high' | 'urgent';
}

export interface KnowledgeBaseResult {
  articles: Array<{
    id: string;
    title: string;
    content: string;
    relevance_score: number;
  }>;
  faqs: Array<{
    question: string;
    answer: string;
    relevance_score: number;
  }>;
}

export interface ResponseGeneration {
  response: string;
  tone: 'professional' | 'friendly' | 'empathetic' | 'technical';
  suggested_actions?: string[];
  escalation_needed: boolean;
  follow_up_required: boolean;
}

export interface ResponseValidation {
  is_appropriate: boolean;
  quality_score: number;
  issues?: string[];
  suggestions?: string[];
}

export interface DeliveredResponse {
  final_response: string;
  delivery_method: string;
  timestamp: Date;
  response_id: string;
  escalated: boolean;
}

/**
 * Main shared store interface for CustomerSupport workflow
 */
export interface SharedStore {
  // User and session info
  user_id: string;
  session_id?: string;
  support_ticket_id?: string;

  // Input data
  inquiry?: CustomerInquiry;
  customer_context?: CustomerContext;

  // Processing results
  intent_classification?: IntentClassification;
  knowledge_base_results?: KnowledgeBaseResult;
  response_generation?: ResponseGeneration;
  response_validation?: ResponseValidation;
  delivered_response?: DeliveredResponse;

  // Configuration
  max_response_length: number;
  enable_escalation: boolean;
  knowledge_base_enabled: boolean;
  
  // Internal tracking
  processing_start_time?: Date;
  processing_end_time?: Date;
  total_processing_time?: number;
}
