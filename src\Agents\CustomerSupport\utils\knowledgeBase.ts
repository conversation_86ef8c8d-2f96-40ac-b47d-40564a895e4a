// src/Agents/CustomerSupport/utils/knowledgeBase.ts

import { KnowledgeBaseResult } from '../types';

/**
 * Mock knowledge base data for demonstration
 * In a real implementation, this would connect to a proper knowledge base system
 */

interface KnowledgeArticle {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
}

// Mock knowledge base articles
const KNOWLEDGE_ARTICLES: KnowledgeArticle[] = [
  {
    id: 'kb001',
    title: 'How to Reset Your Password',
    content: 'To reset your password: 1) Go to the login page, 2) Click "Forgot Password", 3) Enter your email address, 4) Check your email for reset instructions, 5) Follow the link and create a new password.',
    category: 'authentication',
    tags: ['password', 'login', 'reset', 'authentication']
  },
  {
    id: 'kb002',
    title: 'Understanding Your Bill',
    content: 'Your monthly bill includes: base subscription fee, usage charges, any add-on services, taxes, and applicable discounts. The billing cycle runs from the 1st to the last day of each month.',
    category: 'billing',
    tags: ['billing', 'charges', 'subscription', 'invoice']
  },
  {
    id: 'kb003',
    title: 'Troubleshooting Login Issues',
    content: 'Common login problems and solutions: 1) Clear browser cache and cookies, 2) Disable browser extensions, 3) Try incognito/private mode, 4) Check if Caps Lock is on, 5) Verify your internet connection.',
    category: 'technical',
    tags: ['login', 'troubleshooting', 'browser', 'technical']
  },
  {
    id: 'kb004',
    title: 'Cancellation Policy',
    content: 'You can cancel your subscription at any time. Cancellations take effect at the end of your current billing period. No refunds for partial months. To cancel, go to Account Settings > Subscription > Cancel.',
    category: 'cancellation',
    tags: ['cancel', 'subscription', 'policy', 'refund']
  },
  {
    id: 'kb005',
    title: 'Data Export Guide',
    content: 'To export your data: 1) Go to Settings > Data Export, 2) Select the data types you want to export, 3) Choose format (CSV, JSON, PDF), 4) Click "Generate Export", 5) Download when ready.',
    category: 'data',
    tags: ['export', 'data', 'csv', 'download']
  }
];

// Mock FAQ data
const FAQS: FAQ[] = [
  {
    id: 'faq001',
    question: 'How long does it take to process a refund?',
    answer: 'Refunds typically take 3-5 business days to appear in your account, depending on your payment method.',
    category: 'billing',
    tags: ['refund', 'billing', 'processing', 'time']
  },
  {
    id: 'faq002',
    question: 'Can I change my subscription plan?',
    answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately for upgrades, or at the next billing cycle for downgrades.',
    category: 'subscription',
    tags: ['subscription', 'plan', 'upgrade', 'downgrade']
  },
  {
    id: 'faq003',
    question: 'Is my data secure?',
    answer: 'Yes, we use industry-standard encryption and security measures to protect your data. We are SOC 2 compliant and regularly undergo security audits.',
    category: 'security',
    tags: ['security', 'data', 'encryption', 'compliance']
  },
  {
    id: 'faq004',
    question: 'How do I contact support?',
    answer: 'You can reach our support team via <NAME_EMAIL>, through live chat on our website, or by phone at 1-800-SUPPORT during business hours.',
    category: 'contact',
    tags: ['support', 'contact', 'email', 'chat', 'phone']
  }
];

/**
 * Search knowledge base for relevant articles and FAQs
 */
export async function searchKnowledgeBase(
  query: string,
  intent: string,
  maxResults: number = 3
): Promise<KnowledgeBaseResult> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  const queryLower = query.toLowerCase();
  const intentLower = intent.toLowerCase();

  // Score articles based on relevance
  const scoredArticles = KNOWLEDGE_ARTICLES.map(article => {
    let score = 0;
    
    // Check title relevance
    if (article.title.toLowerCase().includes(queryLower)) score += 3;
    
    // Check content relevance
    if (article.content.toLowerCase().includes(queryLower)) score += 2;
    
    // Check category match
    if (article.category.toLowerCase() === intentLower) score += 2;
    
    // Check tag matches
    article.tags.forEach(tag => {
      if (queryLower.includes(tag.toLowerCase()) || tag.toLowerCase().includes(queryLower)) {
        score += 1;
      }
    });

    return {
      ...article,
      relevance_score: score
    };
  })
  .filter(article => article.relevance_score > 0)
  .sort((a, b) => b.relevance_score - a.relevance_score)
  .slice(0, maxResults)
  .map(article => ({
    id: article.id,
    title: article.title,
    content: article.content,
    relevance_score: article.relevance_score
  }));

  // Score FAQs based on relevance
  const scoredFAQs = FAQS.map(faq => {
    let score = 0;
    
    // Check question relevance
    if (faq.question.toLowerCase().includes(queryLower)) score += 3;
    
    // Check answer relevance
    if (faq.answer.toLowerCase().includes(queryLower)) score += 2;
    
    // Check category match
    if (faq.category.toLowerCase() === intentLower) score += 2;
    
    // Check tag matches
    faq.tags.forEach(tag => {
      if (queryLower.includes(tag.toLowerCase()) || tag.toLowerCase().includes(queryLower)) {
        score += 1;
      }
    });

    return {
      ...faq,
      relevance_score: score
    };
  })
  .filter(faq => faq.relevance_score > 0)
  .sort((a, b) => b.relevance_score - a.relevance_score)
  .slice(0, maxResults)
  .map(faq => ({
    question: faq.question,
    answer: faq.answer,
    relevance_score: faq.relevance_score
  }));

  return {
    articles: scoredArticles,
    faqs: scoredFAQs
  };
}

/**
 * Get customer context from mock database
 */
export async function getCustomerContext(customerId: string): Promise<any> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 50));

  // Mock customer data
  const mockCustomers: Record<string, any> = {
    'cust001': {
      customer_id: 'cust001',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      subscription_tier: 'premium',
      account_status: 'active',
      previous_tickets: ['ticket001', 'ticket002'],
      purchase_history: [
        { date: '2024-01-15', amount: 99.99, item: 'Premium Subscription' },
        { date: '2024-02-15', amount: 99.99, item: 'Premium Subscription' }
      ]
    },
    'cust002': {
      customer_id: 'cust002',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '******-0456',
      subscription_tier: 'basic',
      account_status: 'active',
      previous_tickets: [],
      purchase_history: [
        { date: '2024-02-01', amount: 29.99, item: 'Basic Subscription' }
      ]
    }
  };

  return mockCustomers[customerId] || null;
}
