// src/Agents/CustomerSupport/index.ts

import { create_customer_support_flow } from './flow';
import { SharedStore, CustomerInquiry } from './types';

/**
 * Main entry point for the Customer Support Agent
 * 
 * This function provides a simple interface to run the customer support workflow
 * with a customer inquiry and return the generated response.
 */
export async function runCustomerSupportAgent(
  inquiryMessage: string,
  options: {
    userId?: string;
    customerId?: string;
    channel?: 'email' | 'chat' | 'phone' | 'web';
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    maxResponseLength?: number;
    enableEscalation?: boolean;
    knowledgeBaseEnabled?: boolean;
  } = {}
): Promise<string> {
  
  // Set default options
  const {
    userId = 'anonymous',
    customerId,
    channel = 'web',
    priority = 'medium',
    maxResponseLength = 1000,
    enableEscalation = true,
    knowledgeBaseEnabled = true
  } = options;

  // Create the customer inquiry
  const inquiry: CustomerInquiry = {
    id: `inq-${Date.now()}`,
    message: inquiryMessage,
    timestamp: new Date(),
    channel,
    priority,
    customer_id: customerId
  };

  // Initialize the shared store
  const shared: SharedStore = {
    user_id: userId,
    inquiry,
    max_response_length: maxResponseLength,
    enable_escalation: enableEscalation,
    knowledge_base_enabled: knowledgeBaseEnabled
  };

  try {
    // Create and run the customer support flow
    const supportFlow = create_customer_support_flow();
    await supportFlow.run(shared);

    // Return the final response
    if (shared.delivered_response) {
      return shared.delivered_response.final_response;
    } else {
      throw new Error("No response was generated");
    }

  } catch (error) {
    console.error("Customer support workflow failed:", error);
    
    // Return a fallback response
    return "I apologize, but I'm experiencing technical difficulties. Please contact our support team directly for assistance.";
  }
}

/**
 * Advanced interface for running the customer support workflow with full control
 */
export async function runCustomerSupportWorkflow(shared: SharedStore): Promise<SharedStore> {
  const supportFlow = create_customer_support_flow();
  await supportFlow.run(shared);
  return shared;
}

/**
 * Utility function to create a properly structured SharedStore
 */
export function createCustomerSupportStore(
  userId: string,
  inquiry: CustomerInquiry,
  options: {
    sessionId?: string;
    maxResponseLength?: number;
    enableEscalation?: boolean;
    knowledgeBaseEnabled?: boolean;
  } = {}
): SharedStore {
  
  const {
    sessionId,
    maxResponseLength = 1000,
    enableEscalation = true,
    knowledgeBaseEnabled = true
  } = options;

  return {
    user_id: userId,
    session_id: sessionId,
    inquiry,
    max_response_length: maxResponseLength,
    enable_escalation: enableEscalation,
    knowledge_base_enabled: knowledgeBaseEnabled
  };
}

// Export types and flow creation function for advanced usage
export { create_customer_support_flow } from './flow';
export type { 
  SharedStore, 
  CustomerInquiry, 
  CustomerContext,
  IntentClassification,
  KnowledgeBaseResult,
  ResponseGeneration,
  ResponseValidation,
  DeliveredResponse
} from './types';

// Export individual nodes for custom flow creation
export {
  ReceiveInquiry,
  ClassifyIntent,
  RetrieveContext,
  GenerateResponse,
  ValidateResponse,
  DeliverResponse
} from './nodes';

/**
 * Example usage:
 * 
 * // Simple usage
 * const response = await runCustomerSupportAgent(
 *   "I can't log into my account",
 *   { userId: "user123", channel: "chat", priority: "high" }
 * );
 * 
 * // Advanced usage
 * const inquiry: CustomerInquiry = {
 *   id: "inq-001",
 *   message: "I need help with billing",
 *   timestamp: new Date(),
 *   channel: "email",
 *   priority: "medium",
 *   customer_id: "cust-456"
 * };
 * 
 * const store = createCustomerSupportStore("user123", inquiry);
 * const result = await runCustomerSupportWorkflow(store);
 * console.log(result.delivered_response?.final_response);
 */
