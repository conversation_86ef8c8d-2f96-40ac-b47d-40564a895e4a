import React from "react";

interface AdvancedOptionsFormProps {
  showAdvancedOptions: boolean;
  setShowAdvancedOptions: (show: boolean) => void;
  temperature: number;
  setTemperature: (temp: number) => void;
}

export const AdvancedOptionsForm: React.FC<AdvancedOptionsFormProps> = ({
  showAdvancedOptions,
  setShowAdvancedOptions,
  temperature,
  setTemperature,
}) => {
  return (
    <div className="mb-6">
      <button
        type="button"
        onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
        className="flex items-center text-sm text-gray-600 hover:text-gray-900"
      >
        <i
          className={`fa-solid fa-caret-right mr-1 transition-transform ${
            showAdvancedOptions ? "transform rotate-90" : ""
          }`}
        ></i>
        Advanced Options
      </button>

      {showAdvancedOptions && (
        <div className="mt-3 p-4 bg-gray-50 rounded-md border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="max-tokens"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Max Tokens per API Call
              </label>
              <input
                disabled
                type="number"
                id="max-tokens"
                defaultValue="100000"
                min="1000"
                max="200000"
                className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
              />
              <p className="mt-1 text-xs text-gray-500">
                Controls chunking for large codebases
              </p>
            </div>

            <div>
              <label
                htmlFor="model-version"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                LLM Model
              </label>
              <select
                disabled
                id="model-version"
                defaultValue="auto"
                className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
              >
                <option value="auto">Auto</option>
                {/* <option value="UX Pilot-3-7-sonnet">UX Pilot 3.7 Sonnet</option>
              <option value="UX Pilot-3-opus">UX Pilot 3 Opus</option>
              <option value="gpt-4o">GPT-4o</option> */}
              </select>
            </div>

            <div>
              <label
                htmlFor="cache-duration"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Cache Duration
              </label>
              <select
                disabled
                id="cache-duration"
                defaultValue="7"
                className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
              >
                <option value="1">1 day</option>
                <option value="7">7 days</option>
                <option value="30">30 days</option>
                <option value="0">No caching</option>
              </select>
            </div>

            <div>
              <label
                htmlFor="temperature"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Temperature: {temperature.toFixed(1)}
              </label>
              <input
                type="range"
                id="temperature"
                min="0"
                max="1"
                step="0.1"
                defaultValue="0.7"
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                onChange={(e) =>
                  setTemperature(Number(e.target.value))
                }
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Precise</span>
                <span>Creative</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
