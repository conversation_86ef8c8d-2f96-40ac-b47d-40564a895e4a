import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import { TutorialFormState, TutorialFormData, AdvancedOptions } from "@/types/tutorial";
import { 
  getFormFieldValue, 
  getCheckboxValue, 
  getNumericFieldValue, 
  getFloatFieldValue 
} from "@/utils/formHelpers";

export const useTutorialForm = () => {
  const navigate = useNavigate();
  
  const [formState, setFormState] = useState<TutorialFormState>({
    repoUrl: "https://github.com/EarthShaping/testautocode",
    isPrivateRepo: false,
    fileSize: 100,
    showAdvancedOptions: false,
    isGenerating: false,
    isCrawling: false,
    selectedFiles: [],
    temperature: 0.7,
  });

  const updateFormState = (updates: Partial<TutorialFormState>) => {
    setFormState(prev => ({ ...prev, ...updates }));
  };

  const handleSelectedFilesChange = (files: string[]) => {
    updateFormState({ selectedFiles: files });
  };

  const collectFormData = (): TutorialFormData => {
    // Get form values
    const targetAudience = getFormFieldValue("target-audience", "intermediate");
    const contentLanguage = getFormFieldValue("content-language", "english");
    const maxAbstractions = getNumericFieldValue("max-abstractions", 10);
    const tutorialFormat = getFormFieldValue("tutorial-format", "markdown");
    const includeDiagrams = getCheckboxValue("include-diagrams", false);
    const includeExamples = getCheckboxValue("include-examples", false);
    const includeExercises = getCheckboxValue("include-exercises", false);
    const includePatterns = getFormFieldValue("include-patterns", "**/*.ts, **/*.js");
    const excludePatterns = getFormFieldValue("exclude-patterns", "node_modules/**, *.test.js");

    // Get GitHub token if private repo
    let githubToken = "";
    if (formState.isPrivateRepo) {
      githubToken = getFormFieldValue("github-token", "");
    }

    // Advanced options
    let advancedOptions: AdvancedOptions = {
      maxTokens: 100000,
      modelVersion: "UX Pilot-3-7-sonnet",
      cacheDuration: 7,
      temperature: 0.7,
    };

    if (formState.showAdvancedOptions) {
      advancedOptions = {
        maxTokens: getNumericFieldValue("max-tokens", 100000),
        modelVersion: getFormFieldValue("model-version", "UX Pilot-3-7-sonnet"),
        cacheDuration: getNumericFieldValue("cache-duration", 7),
        temperature: getFloatFieldValue("temperature", 0.7),
      };
    }

    return {
      repoUrl: formState.repoUrl,
      isPrivateRepo: formState.isPrivateRepo,
      githubToken,
      fileSize: formState.fileSize,
      includePatterns,
      excludePatterns,
      selectedFiles: formState.selectedFiles,
      targetAudience,
      contentLanguage,
      maxAbstractions,
      tutorialFormat,
      includeDiagrams,
      includeExamples,
      includeExercises,
      advancedOptions,
    };
  };

  const handleStartGeneration = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Starting generation",
      description: "Your tutorial is being generated...",
    });
    updateFormState({ isGenerating: true });

    const formData = collectFormData();

    // Navigate to tutorial creation status page with parameters
    navigate("/dashboard/tutorial-creation-status", {
      state: formData,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formState.repoUrl) {
      toast({
        title: "Repository URL required",
        description: "Please enter a valid GitHub repository URL.",
        variant: "destructive",
      });
      return;
    }

    updateFormState({ isCrawling: true });
  };

  const handleReset = (e: React.FormEvent) => {
    updateFormState({ 
      isCrawling: false, 
      isGenerating: false 
    });
    e.preventDefault();
  };

  return {
    formState,
    updateFormState,
    handleSelectedFilesChange,
    handleStartGeneration,
    handleSubmit,
    handleReset,
  };
};
