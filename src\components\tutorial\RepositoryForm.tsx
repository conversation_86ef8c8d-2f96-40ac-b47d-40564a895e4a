import React from "react";
import { Input } from "@/components/ui/input";
import { DEFAULT_INCLUDE_PATTERNS, DEFAULT_EXCLUDE_PATTERNS } from "@/constants/filePatterns";

interface RepositoryFormProps {
  repoUrl: string;
  setRepoUrl: (url: string) => void;
  isPrivateRepo: boolean;
  setIsPrivateRepo: (isPrivate: boolean) => void;
  fileSize: number;
  setFileSize: (size: number) => void;
  isAnalyzeDisabled: boolean;
}

export const RepositoryForm: React.FC<RepositoryFormProps> = ({
  repoUrl,
  setRepoUrl,
  isPrivateRepo,
  setIsPrivateRepo,
  fileSize,
  setFileSize,
  isAnalyzeDisabled,
}) => {
  return (
    <>
      {/* Repository URL Input */}
      <div className="mb-6">
        <label
          htmlFor="repo-url"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          GitHub Repository URL
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i className="fa-brands fa-github text-gray-400"></i>
          </div>
          <input
            type="text"
            id="repo-url"
            value={repoUrl}
            onChange={(e) => setRepoUrl(e.target.value)}
            className="block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            placeholder="https://github.com/username/repository"
          />
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button
              type="button"
              className="text-tutorial-primary hover:text-blue-700"
              onClick={() => {
                navigator.clipboard.readText().then((text) => {
                  if (text.includes("github.com")) setRepoUrl(text);
                });
              }}
            >
              <i className="fa-solid fa-paste"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Repository Access */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="block text-sm font-medium text-gray-700">
            Repository Access
          </label>
          <span className="text-xs text-gray-500">
            Private repos require authentication
          </span>
        </div>
        <div className="flex space-x-4">
          <div className="flex items-center">
            <input
              type="radio"
              id="public-repo"
              name="repo-access"
              checked={!isPrivateRepo}
              onChange={() => setIsPrivateRepo(false)}
              className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
            />
            <label
              htmlFor="public-repo"
              className="ml-2 block text-sm text-gray-700"
            >
              Public
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="radio"
              id="private-repo"
              name="repo-access"
              checked={isPrivateRepo}
              onChange={() => setIsPrivateRepo(true)}
              className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
            />
            <label
              htmlFor="private-repo"
              className="ml-2 block text-sm text-gray-700"
            >
              Private
            </label>
          </div>
        </div>
      </div>

      {/* Authentication Section */}
      {isPrivateRepo && (
        <div className="mb-6">
          <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Authentication Options
            </h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center mb-2">
                  <input
                    type="radio"
                    id="personal-token"
                    name="auth-method"
                    defaultChecked
                    className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
                  />
                  <label
                    htmlFor="personal-token"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Personal Access Token
                  </label>
                </div>
                <div className="relative">
                  <input
                    type="password"
                    id="github-token"
                    className="block w-full pr-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                    placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <i className="fa-regular fa-eye"></i>
                    </button>
                  </div>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Needs 'repo' scope for private repositories
                </p>
              </div>

              <div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="oauth"
                    name="auth-method"
                    className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300"
                  />
                  <label
                    htmlFor="oauth"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    GitHub OAuth
                  </label>
                </div>
                <button
                  type="button"
                  className="mt-2 flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <i className="fa-brands fa-github mr-2"></i>
                  Sign in with GitHub
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* File Patterns Section */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-3">
          File Selection
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {/* Include Patterns */}
          <div>
            <label
              htmlFor="include-patterns"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Include Patterns
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="fa-solid fa-check-circle text-green-500"></i>
              </div>
              <Input
                id="include-patterns"
                defaultValue={DEFAULT_INCLUDE_PATTERNS.join(", ")}
                type="text"
                className="block w-full pl-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                placeholder="*.js, src/**/*.jsx"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Comma-separated glob patterns to include
            </p>
          </div>

          {/* Exclude Patterns */}
          <div>
            <label
              htmlFor="exclude-patterns"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Exclude Patterns
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="fa-solid fa-ban text-red-500"></i>
              </div>
              <Input
                id="exclude-patterns"
                defaultValue={DEFAULT_EXCLUDE_PATTERNS.join(", ")}
                type="text"
                className="block w-full pl-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
                placeholder="node_modules/**, *.test.js"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Patterns to exclude from analysis
            </p>
          </div>
        </div>

        {/* File Size Limit */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <label
              htmlFor="file-size-limit"
              className="block text-sm font-medium text-gray-700"
            >
              Maximum File Size
            </label>
            <span className="text-sm text-gray-500">{fileSize} KB</span>
          </div>
          <input
            type="range"
            id="file-size-limit"
            min="50"
            max="2000"
            value={fileSize}
            onChange={(e) => setFileSize(Number(e.target.value))}
            step="50"
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-2"
          />
          <p className="mt-1 text-xs text-gray-500">
            Files larger than this will be skipped during analysis
          </p>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isAnalyzeDisabled}
          className={`px-6 py-3 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tutorial-primary ${
            isAnalyzeDisabled
              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
              : "bg-tutorial-primary text-white hover:bg-blue-700"
          }`}
        >
          <i className="fa-solid fa-flag-checkered mr-2"></i>
          Analyze Repository
        </button>
      </div>
    </>
  );
};
