// src/Agents/CustomerSupport/nodes/ValidateResponse.ts

import { SharedStore, ResponseValidation } from "../types";
import { Node } from '../../../pocketflow';
import { callLlm_openrouter } from "../../shared/callLlm_openrouter";
import { buildValidateResponsePrompt } from "../prompts/validateResponse";
import { emitProgress, emitGraphStatus, emitSupportEvent } from "../utils/events";

/**
 * ValidateResponse Node
 * 
 * This node validates the generated customer support response for:
 * - Quality and appropriateness
 * - Accuracy and completeness
 * - Tone and professionalism
 * - Policy compliance
 * - Potential issues or improvements
 */
export class ValidateResponse extends Node<SharedStore> {
  
  constructor(maxRetries: number = 2, waitTime: number = 3) {
    super(maxRetries, waitTime);
  }

  async prep(shared: SharedStore): Promise<string> {
    emitGraphStatus("ValidateResponse", 0, "Preparing response validation");
    
    if (!shared.inquiry || !shared.intent_classification || !shared.response_generation) {
      throw new Error("Missing required data for response validation");
    }
    
    // Prepare context for validation
    const customerContext = shared.customer_context ? 
      JSON.stringify(shared.customer_context, null, 2) : 
      "No customer context available";
    
    const intentClassification = JSON.stringify(shared.intent_classification, null, 2);
    
    emitGraphStatus("ValidateResponse", 30, "Building validation prompt");
    
    const prompt = buildValidateResponsePrompt(
      shared.inquiry.message,
      intentClassification,
      shared.response_generation.response,
      customerContext
    );
    
    return JSON.stringify({
      prompt,
      inquiry_id: shared.inquiry.id,
      ticket_id: shared.support_ticket_id,
      response_length: shared.response_generation.response.length
    });
  }

  async exec(args: string): Promise<string> {
    const parsedArgs = JSON.parse(args);
    
    emitGraphStatus("ValidateResponse", 40, "Calling LLM for response validation");
    
    try {
      const response = await callLlm_openrouter({
        prompt: parsedArgs.prompt,
        temperature: 0.1, // Low temperature for consistent validation
        model: "google/gemini-2.5-flash-preview-05-20",
        use_cache: true,
        tutorial_id: parsedArgs.ticket_id || "unknown"
      });
      
      emitGraphStatus("ValidateResponse", 80, "Processing validation results");
      
      // Parse the JSON response from the LLM
      let validationData;
      try {
        // Extract JSON from the response if it's wrapped in markdown
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || 
                         response.match(/\{[\s\S]*\}/);
        
        if (jsonMatch) {
          validationData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } else {
          validationData = JSON.parse(response);
        }
      } catch (parseError) {
        console.warn("Failed to parse validation response as JSON, using fallback validation");
        // Fallback validation - assume response is acceptable
        validationData = {
          is_appropriate: true,
          quality_score: 7.0,
          accuracy_score: 7,
          completeness_score: 7,
          clarity_score: 7,
          tone_score: 7,
          professionalism_score: 8,
          actionability_score: 6,
          issues: ["Unable to perform detailed validation due to parsing error"],
          suggestions: ["Manual review recommended"],
          reasoning: "Fallback validation due to parsing error"
        };
      }
      
      // Validate and normalize the validation data
      validationData = this.normalizeValidationData(validationData);
      
      emitGraphStatus("ValidateResponse", 90, `Validation completed - Quality score: ${validationData.quality_score}/10`);
      
      return JSON.stringify(validationData);
      
    } catch (error) {
      console.error("Error validating response:", error);
      
      // Return a fallback validation that flags for manual review
      const fallbackValidation = {
        is_appropriate: false,
        quality_score: 5.0,
        accuracy_score: 5,
        completeness_score: 5,
        clarity_score: 5,
        tone_score: 5,
        professionalism_score: 5,
        actionability_score: 5,
        issues: [`Validation failed: ${error.message}`],
        suggestions: ["Manual review required", "Consider regenerating response"],
        reasoning: "Validation process encountered an error"
      };
      
      return JSON.stringify(fallbackValidation);
    }
  }

  async post(
    shared: SharedStore,
    _: string,
    execRes: string,
  ): Promise<string | undefined> {
    
    const validationData = JSON.parse(execRes);
    
    // Create the response validation object
    const responseValidation: ResponseValidation = {
      is_appropriate: validationData.is_appropriate,
      quality_score: validationData.quality_score,
      issues: validationData.issues || [],
      suggestions: validationData.suggestions || []
    };
    
    // Store in shared store
    shared.response_validation = responseValidation;
    
    emitGraphStatus("ValidateResponse", 100, "Response validation completed");
    
    // Determine next action based on validation results
    const qualityThreshold = 6.0; // Minimum acceptable quality score
    const needsImprovement = !responseValidation.is_appropriate || 
                           responseValidation.quality_score < qualityThreshold;
    
    if (needsImprovement) {
      emitProgress("Inquiry Processing", 75, `Response validation failed (score: ${responseValidation.quality_score}/10) - needs improvement`);
      
      // Log validation failure
      emitSupportEvent("response_validation_failed", {
        inquiry_id: shared.inquiry?.id,
        ticket_id: shared.support_ticket_id,
        quality_score: responseValidation.quality_score,
        is_appropriate: responseValidation.is_appropriate,
        issues: responseValidation.issues,
        suggestions: responseValidation.suggestions
      });
      
      console.log(`⚠ Response validation failed: Quality score ${responseValidation.quality_score}/10`);
      console.log(`Issues: ${responseValidation.issues.join(', ')}`);
      
      // In a real implementation, you might:
      // - Return "regenerate" to trigger response regeneration
      // - Return "escalate" to escalate to human agent
      // - Return "manual_review" for manual intervention
      
      // For now, we'll continue but flag for manual review
      if (shared.response_generation) {
        shared.response_generation.escalation_needed = true;
      }
      
    } else {
      emitProgress("Inquiry Processing", 85, `Response validated successfully (score: ${responseValidation.quality_score}/10)`);
      
      console.log(`✓ Response validated: Quality score ${responseValidation.quality_score}/10`);
    }
    
    // Log validation results
    emitSupportEvent("response_validated", {
      inquiry_id: shared.inquiry?.id,
      ticket_id: shared.support_ticket_id,
      quality_score: responseValidation.quality_score,
      is_appropriate: responseValidation.is_appropriate,
      passed_validation: !needsImprovement,
      issues_count: responseValidation.issues.length,
      suggestions_count: responseValidation.suggestions.length
    });
    
    return "default";
  }

  /**
   * Normalize and validate the validation data structure
   */
  private normalizeValidationData(data: any): any {
    // Ensure all scores are numbers between 0 and 10
    const normalizeScore = (score: any): number => {
      const num = Number(score);
      if (isNaN(num)) return 5.0;
      return Math.max(0, Math.min(10, num));
    };
    
    // Calculate overall quality score if not provided
    let qualityScore = data.quality_score;
    if (typeof qualityScore !== 'number' || isNaN(qualityScore)) {
      const scores = [
        data.accuracy_score,
        data.completeness_score,
        data.clarity_score,
        data.tone_score,
        data.professionalism_score,
        data.actionability_score
      ].map(normalizeScore);
      
      qualityScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    }
    
    return {
      is_appropriate: Boolean(data.is_appropriate),
      quality_score: normalizeScore(qualityScore),
      accuracy_score: normalizeScore(data.accuracy_score),
      completeness_score: normalizeScore(data.completeness_score),
      clarity_score: normalizeScore(data.clarity_score),
      tone_score: normalizeScore(data.tone_score),
      professionalism_score: normalizeScore(data.professionalism_score),
      actionability_score: normalizeScore(data.actionability_score),
      issues: Array.isArray(data.issues) ? data.issues : [],
      suggestions: Array.isArray(data.suggestions) ? data.suggestions : [],
      reasoning: data.reasoning || "Validation completed"
    };
  }
}
