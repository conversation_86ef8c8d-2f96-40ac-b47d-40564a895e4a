// src/Agents/CustomerSupport/nodes/RetrieveContext.ts

import { SharedStore } from "../types";
import { Node } from '../../../pocketflow';
import { searchKnowledgeBase, getCustomerContext } from "../utils/knowledgeBase";
import { emitProgress, emitGraphStatus, emitSupportEvent } from "../utils/events";

/**
 * RetrieveContext Node
 * 
 * This node retrieves relevant context for the customer inquiry including:
 * - Customer information and history
 * - Relevant knowledge base articles
 * - FAQ entries
 * - Previous support interactions
 */
export class RetrieveContext extends Node<SharedStore> {
  
  constructor(maxRetries: number = 2, waitTime: number = 3) {
    super(maxRetries, waitTime);
  }

  async prep(shared: SharedStore): Promise<string> {
    emitGraphStatus("RetrieveContext", 0, "Preparing context retrieval");
    
    if (!shared.inquiry || !shared.intent_classification) {
      throw new Error("Missing inquiry or intent classification for context retrieval");
    }
    
    emitGraphStatus("RetrieveContext", 20, "Gathering retrieval parameters");
    
    return JSON.stringify({
      inquiry_message: shared.inquiry.message,
      intent: shared.intent_classification.intent,
      customer_id: shared.inquiry.customer_id,
      urgency_level: shared.intent_classification.urgency_level,
      knowledge_base_enabled: shared.knowledge_base_enabled
    });
  }

  async exec(args: string): Promise<string> {
    const parsedArgs = JSON.parse(args);
    
    emitGraphStatus("RetrieveContext", 30, "Starting context retrieval");
    
    const results: any = {
      customer_context: null,
      knowledge_base_results: null,
      retrieval_timestamp: new Date().toISOString()
    };
    
    try {
      // Retrieve customer context if customer ID is available
      if (parsedArgs.customer_id) {
        emitGraphStatus("RetrieveContext", 40, "Retrieving customer information");
        
        results.customer_context = await getCustomerContext(parsedArgs.customer_id);
        
        if (results.customer_context) {
          emitGraphStatus("RetrieveContext", 50, "Customer context retrieved");
        } else {
          emitGraphStatus("RetrieveContext", 50, "No customer context found");
        }
      }
      
      // Search knowledge base if enabled
      if (parsedArgs.knowledge_base_enabled) {
        emitGraphStatus("RetrieveContext", 60, "Searching knowledge base");
        
        results.knowledge_base_results = await searchKnowledgeBase(
          parsedArgs.inquiry_message,
          parsedArgs.intent,
          3 // Max results
        );
        
        const totalResults = results.knowledge_base_results.articles.length + 
                           results.knowledge_base_results.faqs.length;
        
        emitGraphStatus("RetrieveContext", 80, `Found ${totalResults} relevant knowledge base entries`);
      } else {
        emitGraphStatus("RetrieveContext", 70, "Knowledge base search disabled");
        results.knowledge_base_results = { articles: [], faqs: [] };
      }
      
      emitGraphStatus("RetrieveContext", 90, "Context retrieval completed");
      
      return JSON.stringify(results);
      
    } catch (error) {
      console.error("Error retrieving context:", error);
      
      // Return partial results if some retrieval failed
      emitGraphStatus("RetrieveContext", 85, "Context retrieval completed with errors");
      
      return JSON.stringify({
        customer_context: results.customer_context,
        knowledge_base_results: results.knowledge_base_results || { articles: [], faqs: [] },
        retrieval_timestamp: new Date().toISOString(),
        errors: [error.message]
      });
    }
  }

  async post(
    shared: SharedStore,
    _: string,
    execRes: string,
  ): Promise<string | undefined> {
    
    const retrievalResults = JSON.parse(execRes);
    
    // Store customer context if retrieved
    if (retrievalResults.customer_context) {
      shared.customer_context = retrievalResults.customer_context;
    }
    
    // Store knowledge base results
    shared.knowledge_base_results = retrievalResults.knowledge_base_results;
    
    emitGraphStatus("RetrieveContext", 100, "Context stored successfully");
    
    // Calculate context summary for progress reporting
    const contextSummary = [];
    if (shared.customer_context) {
      contextSummary.push(`Customer: ${shared.customer_context.name || 'Unknown'}`);
    }
    
    const kbResults = shared.knowledge_base_results;
    if (kbResults && (kbResults.articles.length > 0 || kbResults.faqs.length > 0)) {
      contextSummary.push(`KB: ${kbResults.articles.length} articles, ${kbResults.faqs.length} FAQs`);
    }
    
    const contextMessage = contextSummary.length > 0 ? 
      contextSummary.join(', ') : 
      'No additional context found';
    
    emitProgress("Inquiry Processing", 50, `Context retrieved: ${contextMessage}`);
    
    // Log context retrieval results
    emitSupportEvent("context_retrieved", {
      inquiry_id: shared.inquiry?.id,
      ticket_id: shared.support_ticket_id,
      has_customer_context: !!shared.customer_context,
      knowledge_base_articles: kbResults?.articles.length || 0,
      knowledge_base_faqs: kbResults?.faqs.length || 0,
      errors: retrievalResults.errors || []
    });
    
    console.log(`✓ Context retrieved: ${contextMessage}`);
    
    return "default";
  }
}
