Current Implementation
1. Database Function: can_user_create_tutorial_monthly(user_id) This is the main function that checks monthly limits:

Gets user details from user_details table
Gets tier limits from tier_settings table
Handles billing cycle resets automatically
Returns TRUE if user can create tutorials, FALSE if they've hit the limit
Supports unlimited tutorials (when max_tutorials_per_month = -1)
2. React Hook: useMonthlyTutorialUsage This hook provides real-time usage data in the frontend:

Shows monthlyTutorialsCreated vs maxTutorialsPerMonth
Handles billing cycle information
Updates automatically when user data changes
Used by the MonthlyUsageDisplay component
3. Usage in Components The MonthlyUsageDisplay component shows:

Progress bar for tutorial usage
Remaining tutorials count
"Unlimited" display for premium tiers
Billing cycle end date
How It Works
For Subscribed Users:

Uses actual Stripe billing cycles (updated by check-subscription function)
Monthly count resets based on subscription billing period
Different tiers have different limits (Spark: 5, Propel: 50, Apex: unlimited)
For Free Users:

Uses calendar month cycles
Resets on the 1st of each month
Limited to tier settings (typically 5 tutorials for Spark)
Key Functions to Use

// Check if user can create tutorial (backend)
SELECT can_user_create_tutorial_monthly('user-uuid-here');

// Get usage data (frontend)
const { monthlyTutorialsCreated, maxTutorialsPerMonth } = useMonthlyTutorialUsage();

// Check if at limit
const isAtLimit = maxTutorialsPerMonth !== -1 && monthlyTutorialsCreated >= maxTutorialsPerMonth;
The system automatically handles billing cycle resets and provides both database-level and frontend-level checking mechanisms.