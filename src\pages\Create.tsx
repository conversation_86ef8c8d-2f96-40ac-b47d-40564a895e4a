import React from "react";
import { useNavigate } from "react-router-dom";

import { toast } from "@/hooks/use-toast";
import GitHubRepoCrawler from "@/components/tutorial/GitHubRepoCrawler";
import { Button } from "@/components/ui/button";
import { useTrialStatus } from "@/hooks/useTrialStatus";
import { useMonthlyTutorialUsage } from "@/hooks/useMonthlyTutorialUsage";
import { SubscriptionPlans } from "@/components/subscription/SubscriptionPlans";
import { useSubscription } from "@/hooks/useSubscription";
import { Skeleton } from "@/components/ui/skeleton";

// Custom hooks and utilities
import { useTutorialForm } from "@/hooks/useTutorialForm";
import { parsePatterns } from "@/utils/formHelpers";
import {
  DEFAULT_INCLUDE_PATTERNS,
  DEFAULT_EXCLUDE_PATTERNS,
} from "@/constants/filePatterns";

// Components
import { LimitAlerts } from "@/components/tutorial/LimitAlerts";
import { RepositoryForm } from "@/components/tutorial/RepositoryForm";
import { TutorialConfigForm } from "@/components/tutorial/TutorialConfigForm";
import { AdvancedOptionsForm } from "@/components/tutorial/AdvancedOptionsForm";
import { RecentTutorials } from "@/components/tutorial/RecentTutorials";
import { HowItWorks } from "@/components/tutorial/HowItWorks";

const Create = () => {
  const { trialStatus, loading } = useTrialStatus();
  const {
    monthlyTutorialsCreated,
    maxTutorialsPerMonth,
    loading: usageLoading,
  } = useMonthlyTutorialUsage();
  const { subscribed, loading: subscriptionLoading } = useSubscription();

  // Use custom hook for form management
  const {
    formState,
    updateFormState,
    handleSelectedFilesChange,
    handleStartGeneration,
    handleSubmit,
    handleReset,
  } = useTutorialForm();

  // Calculate monthly limit status at component level
  const hasReachedMonthlyLimit =
    !usageLoading &&
    maxTutorialsPerMonth !== -1 &&
    monthlyTutorialsCreated >= maxTutorialsPerMonth;
  const remainingTutorials =
    maxTutorialsPerMonth === -1
      ? Infinity
      : maxTutorialsPerMonth - monthlyTutorialsCreated;

  // Calculate disabled states
  const isAnalyzeDisabled =
    !formState.repoUrl ||
    (!loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial) ||
    hasReachedMonthlyLimit;
  const isGenerateDisabled =
    (!loading && trialStatus.isInTrial && !trialStatus.canCreateTutorial) ||
    hasReachedMonthlyLimit;

  return (
    <>
      {/* Page Title */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">
          Generate Tutorials from GitHub Repositories
        </h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Transform any GitHub repository into a comprehensive,
          beginner-friendly tutorial with the power of AI.
        </p>
      </div>

      {subscriptionLoading ? (
        <div className="flex justify-center items-center h-12">
          <Skeleton className="h-4 w-1/5" />
        </div>
      ) : (
        !subscribed && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-6 text-center">
              Upgrade Your Plan
            </h2>
            <SubscriptionPlans />
          </div>
        )
      )}

      {!subscriptionLoading && subscribed && (
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Form Section */}
          <div className="w-full lg:w-2/3 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold mb-6 text-gray-800">
              Configure Your Tutorial
            </h2>

            <LimitAlerts
              trialStatus={trialStatus}
              loading={loading}
              hasReachedMonthlyLimit={hasReachedMonthlyLimit}
              maxTutorialsPerMonth={maxTutorialsPerMonth}
              monthlyTutorialsCreated={monthlyTutorialsCreated}
              remainingTutorials={remainingTutorials}
              usageLoading={usageLoading}
            />

            {/* Form Github  */}

            {!formState.isCrawling && (
              <form onSubmit={handleSubmit}>
                <RepositoryForm
                  repoUrl={formState.repoUrl}
                  setRepoUrl={(url) => updateFormState({ repoUrl: url })}
                  isPrivateRepo={formState.isPrivateRepo}
                  setIsPrivateRepo={(isPrivate) =>
                    updateFormState({ isPrivateRepo: isPrivate })
                  }
                  fileSize={formState.fileSize}
                  setFileSize={(size) => updateFormState({ fileSize: size })}
                  isAnalyzeDisabled={isAnalyzeDisabled}
                />
              </form>
            )}
            {/* Repository Structure Preview */}
            {formState.isCrawling && (
              <>
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Repository Structure
                    </label>
                    <span className="text-xs text-gray-500">
                      Select files to include in analysis
                    </span>
                  </div>

                  <GitHubRepoCrawler
                    repoUrl={formState.repoUrl}
                    githubToken={
                      formState.isPrivateRepo
                        ? (
                            document.getElementById(
                              "github-token"
                            ) as HTMLInputElement
                          )?.value
                        : undefined
                    }
                    includePatterns={parsePatterns(
                      (
                        document.getElementById(
                          "include-patterns"
                        ) as HTMLInputElement
                      )?.value || DEFAULT_INCLUDE_PATTERNS.join(", ")
                    )}
                    excludePatterns={parsePatterns(
                      (
                        document.getElementById(
                          "exclude-patterns"
                        ) as HTMLInputElement
                      )?.value || DEFAULT_EXCLUDE_PATTERNS.join(", ")
                    )}
                    onSelectionChange={handleSelectedFilesChange}
                  />
                </div>

                <form onSubmit={handleStartGeneration} onReset={handleReset}>
                  <TutorialConfigForm />

                  <AdvancedOptionsForm
                    showAdvancedOptions={formState.showAdvancedOptions}
                    setShowAdvancedOptions={(show) =>
                      updateFormState({ showAdvancedOptions: show })
                    }
                    temperature={formState.temperature}
                    setTemperature={(temp) =>
                      updateFormState({ temperature: temp })
                    }
                  />

                  <div className="flex justify-between space-x-4">
                    {/* Reset Button */}
                    <div className="flex justify-end ">
                      <Button variant="outline" type="reset">
                        <i className="fa-solid fa-left-long"></i>
                        Reset
                      </Button>
                    </div>
                    {/* Submit Button */}
                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        disabled={isGenerateDisabled}
                        className={`px-6 py-3 font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tutorial-primary ${
                          isGenerateDisabled
                            ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                            : "bg-tutorial-primary text-white"
                        }`}
                      >
                        <i className="fa-solid fa-wand-magic-sparkles mr-2"></i>
                        {isGenerateDisabled &&
                        trialStatus.isInTrial &&
                        !trialStatus.canCreateTutorial
                          ? "Upgrade to Continue"
                          : isGenerateDisabled && hasReachedMonthlyLimit
                          ? "Monthly Limit Reached"
                          : "Generate Tutorial"}
                      </Button>
                    </div>
                  </div>
                </form>
              </>
            )}
          </div>

          {/* Right Sidebar */}
          <div className="w-full lg:w-1/3 space-y-6">
            <RecentTutorials />
            <HowItWorks />
          </div>
        </div>
      )}
    </>
  );
};

export default Create;
