// src/Agents/CustomerSupport/prompts/classifyIntent.ts

import { buildPrompt } from '../../../pocketflow/utils/buildPrompt';

export const CLASSIFY_INTENT_PROMPT = `
You are a customer support intent classifier. Your job is to analyze customer inquiries and classify them into appropriate categories.

## CLASSIFICATION CATEGORIES:

### Primary Intents:
- **billing**: Questions about payments, invoices, charges, refunds, subscription changes
- **technical**: Technical issues, bugs, feature problems, integration help
- **general**: General questions, how-to guides, product information
- **complaint**: Complaints about service, product issues, dissatisfaction
- **feature_request**: Requests for new features or improvements
- **cancellation**: Requests to cancel service or subscription

### Urgency Levels:
- **low**: General questions, non-urgent requests
- **medium**: Standard issues that need attention within business hours
- **high**: Issues affecting customer's ability to use the service
- **urgent**: Critical issues, security concerns, or angry customers

## INSTRUCTIONS:
1. Read the customer inquiry carefully
2. Identify the primary intent from the categories above
3. Determine the urgency level based on the tone and content
4. Assign a confidence score (0.0 to 1.0)
5. Optionally provide a sub-category for more specific classification

## INPUT:
Customer Inquiry: \${inquiry_message}
Customer Context: \${customer_context}

## OUTPUT FORMAT:
Return your response in JSON format:
{
  "intent": "primary_intent_category",
  "confidence": 0.95,
  "sub_category": "specific_subcategory_if_applicable",
  "urgency_level": "urgency_level",
  "reasoning": "Brief explanation of your classification"
}

## EXAMPLES:

Input: "I can't log into my account and I have an important meeting in 30 minutes"
Output: {
  "intent": "technical",
  "confidence": 0.95,
  "sub_category": "authentication",
  "urgency_level": "high",
  "reasoning": "Login issue with time pressure indicates high urgency technical problem"
}

Input: "How do I export my data to CSV format?"
Output: {
  "intent": "general",
  "confidence": 0.90,
  "sub_category": "how_to",
  "urgency_level": "low",
  "reasoning": "Standard how-to question with no urgency indicators"
}

Input: "I was charged twice this month and need a refund immediately"
Output: {
  "intent": "billing",
  "confidence": 0.98,
  "sub_category": "refund",
  "urgency_level": "medium",
  "reasoning": "Billing issue with duplicate charges, standard urgency for financial matters"
}

Now classify the following inquiry:
`;

export const buildClassifyIntentPrompt = (inquiryMessage: string, customerContext: string): string => {
  return buildPrompt(CLASSIFY_INTENT_PROMPT, {
    inquiry_message: inquiryMessage,
    customer_context: customerContext
  });
};
