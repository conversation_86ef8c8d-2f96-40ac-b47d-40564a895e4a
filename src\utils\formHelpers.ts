/**
 * Utility functions for form handling
 */

/**
 * Parse comma-separated patterns string into array
 */
export const parsePatterns = (patternsString: string): string[] => {
  return patternsString
    .split(",")
    .map((pattern) => pattern.trim())
    .filter((pattern) => pattern.length > 0);
};

/**
 * Get form field value by ID
 */
export const getFormFieldValue = (id: string, defaultValue: string = ""): string => {
  const element = document.getElementById(id) as HTMLInputElement | HTMLSelectElement;
  return element?.value || defaultValue;
};

/**
 * Get checkbox field value by ID
 */
export const getCheckboxValue = (id: string, defaultValue: boolean = false): boolean => {
  const element = document.getElementById(id) as HTMLInputElement;
  return element?.checked || defaultValue;
};

/**
 * Get numeric form field value by ID
 */
export const getNumericFieldValue = (id: string, defaultValue: number): number => {
  const element = document.getElementById(id) as HTMLInputElement;
  return parseInt(element?.value || defaultValue.toString());
};

/**
 * Get float form field value by ID
 */
export const getFloatFieldValue = (id: string, defaultValue: number): number => {
  const element = document.getElementById(id) as HTMLInputElement;
  return parseFloat(element?.value || defaultValue.toString());
};
