import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

interface LimitAlertsProps {
  trialStatus: {
    isInTrial: boolean;
    canCreateTutorial: boolean;
    tutorialsLimit: number;
  };
  loading: boolean;
  hasReachedMonthlyLimit: boolean;
  maxTutorialsPerMonth: number;
  monthlyTutorialsCreated: number;
  remainingTutorials: number;
  usageLoading: boolean;
}

export const LimitAlerts: React.FC<LimitAlertsProps> = ({
  trialStatus,
  loading,
  hasReachedMonthlyLimit,
  maxTutorialsPerMonth,
  monthlyTutorialsCreated,
  remainingTutorials,
  usageLoading,
}) => {
  return (
    <>
      {/* Trial Limit Warning */}
      {!loading &&
        trialStatus.isInTrial &&
        !trialStatus.canCreateTutorial && (
          <Alert className="mb-6 border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              You've reached your trial limit of{" "}
              {trialStatus.tutorialsLimit} tutorials. Upgrade your account
              to create unlimited tutorials.
            </AlertDescription>
          </Alert>
        )}

      {/* Monthly Limit Warning */}
      {hasReachedMonthlyLimit && (
        <Alert className="mb-6 border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            You've reached your monthly limit of {maxTutorialsPerMonth} tutorials. 
            Upgrade your plan or wait for your next billing cycle to create more tutorials.
          </AlertDescription>
        </Alert>
      )}

      {/* Monthly Usage Info */}
      {!usageLoading && maxTutorialsPerMonth !== -1 && !hasReachedMonthlyLimit && (
        <Alert className="mb-6 border-blue-200 bg-blue-50">
          <AlertDescription className="text-blue-800">
            You have {remainingTutorials} tutorial{remainingTutorials !== 1 ? 's' : ''} remaining this month 
            ({monthlyTutorialsCreated}/{maxTutorialsPerMonth} used).
          </AlertDescription>
        </Alert>
      )}
    </>
  );
};
