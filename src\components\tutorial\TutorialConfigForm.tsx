import React from "react";

interface TutorialConfigFormProps {
  // No specific props needed as this component manages its own form fields
}

export const TutorialConfigForm: React.FC<TutorialConfigFormProps> = () => {
  return (
    <div className="mb-6">
      <h3 className="text-sm font-medium text-gray-700 mb-3">
        Tutorial Configuration
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {/* Target Audience */}
        <div>
          <label
            htmlFor="target-audience"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Target Audience
          </label>
          <select
            id="target-audience"
            defaultValue="intermediate"
            className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
          >
            {/* <option value="beginner">Beginner (No prior knowledge)</option> */}
            <option value="intermediate">
              Intermediate (Some familiarity)
            </option>
            {/* <option value="advanced">Advanced (Experienced developers)</option> */}
          </select>
        </div>

        {/* Content Language */}
        <div>
          <label
            htmlFor="content-language"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Content Language
          </label>
          <select
            id="content-language"
            defaultValue="auto"
            className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
          >
            <option value="english">English</option>
            <option value="german">German</option>
            <option value="italian">Italian</option>
            <option value="chinese">Chinese</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {/* Max Abstractions */}
        <div>
          <label
            htmlFor="max-abstractions"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Max Core Concepts
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="fa-solid fa-layer-group text-gray-400"></i>
            </div>
            <input
              type="number"
              id="max-abstractions"
              defaultValue="10"
              min="3"
              max="15"
              className="block w-full pl-10 py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
            />
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Number of core abstractions to identify (3-15)
          </p>
        </div>

        {/* Tutorial Format */}
        <div>
          <label
            htmlFor="tutorial-format"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Output Format
          </label>
          <select
            id="tutorial-format"
            defaultValue="markdown"
            className="block w-full py-2 border border-gray-300 rounded-md focus:ring-tutorial-primary focus:border-tutorial-primary"
          >
            <option value="markdown">Markdown</option>
            {/* <option value="html">HTML</option>
          <option value="pdf">PDF</option> */}
          </select>
        </div>
      </div>

      {/* Additional Options */}
      <div className="space-y-2">
        <div className="flex items-center">
          <input
            disabled
            type="checkbox"
            id="include-diagrams"
            defaultChecked
            className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300 rounded"
          />
          <label
            htmlFor="include-diagrams"
            className="ml-2 block text-sm text-gray-700"
          >
            Include architecture diagrams
          </label>
        </div>
        <div className="flex items-center">
          <input
            disabled
            type="checkbox"
            id="include-examples"
            defaultChecked
            className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300 rounded"
          />
          <label
            htmlFor="include-examples"
            className="ml-2 block text-sm text-gray-700"
          >
            Add practical code examples
          </label>
        </div>
        <div className="flex items-center">
          <input
            title="Coming soon"
            disabled
            type="checkbox"
            id="include-exercises"
            className="h-4 w-4 text-tutorial-primary focus:ring-tutorial-primary border-gray-300 rounded"
          />
          <label
            htmlFor="include-exercises"
            className="ml-2 block text-sm text-gray-700"
          >
            Include exercises and challenges
          </label>
        </div>
      </div>
    </div>
  );
};
