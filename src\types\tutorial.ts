/**
 * Types for tutorial creation and configuration
 */

export interface TutorialFormData {
  repoUrl: string;
  isPrivateRepo: boolean;
  githubToken: string;
  fileSize: number;
  includePatterns: string;
  excludePatterns: string;
  selectedFiles: string[];
  targetAudience: string;
  contentLanguage: string;
  maxAbstractions: number;
  tutorialFormat: string;
  includeDiagrams: boolean;
  includeExamples: boolean;
  includeExercises: boolean;
  advancedOptions: AdvancedOptions;
}

export interface AdvancedOptions {
  maxTokens: number;
  modelVersion: string;
  cacheDuration: number;
  temperature: number;
}

export interface TutorialFormState {
  repoUrl: string;
  isPrivateRepo: boolean;
  fileSize: number;
  showAdvancedOptions: boolean;
  isGenerating: boolean;
  isCrawling: boolean;
  selectedFiles: string[];
  temperature: number;
}

export interface RecentTutorial {
  id: string;
  title: string;
  description: string;
  icon: string;
  iconColor: string;
  chapters: number;
  generatedAt: string;
  url: string;
}
