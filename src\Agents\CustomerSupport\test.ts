// src/Agents/CustomerSupport/test.ts

import { runCustomerSupportAgent, createCustomerSupportStore, runCustomerSupportWorkflow } from './index';
import { CustomerInquiry } from './types';

/**
 * Test the customer support agent with various scenarios
 */
async function testCustomerSupportAgent(): Promise<void> {
  console.log('=== Testing Customer Support Agent ===\n');

  // Test case 1: Login issue (technical, high urgency)
  console.log('Test Case 1: Technical Issue - Login Problem');
  console.log('User: I can\'t log into my account and I have an important meeting in 30 minutes');
  try {
    const response1 = await runCustomerSupportAgent(
      "I can't log into my account and I have an important meeting in 30 minutes",
      {
        userId: "user123",
        customerId: "cust001",
        channel: "chat",
        priority: "high"
      }
    );
    console.log(`Assistant: ${response1}\n`);
  } catch (error) {
    console.error(`Error: ${error.message}\n`);
  }

  // Test case 2: Billing inquiry
  console.log('Test Case 2: Billing Inquiry');
  console.log('User: I was charged twice this month and need a refund');
  try {
    const response2 = await runCustomerSupportAgent(
      "I was charged twice this month and need a refund",
      {
        userId: "user456",
        customerId: "cust002",
        channel: "email",
        priority: "medium"
      }
    );
    console.log(`Assistant: ${response2}\n`);
  } catch (error) {
    console.error(`Error: ${error.message}\n`);
  }

  // Test case 3: General question
  console.log('Test Case 3: General Question');
  console.log('User: How do I export my data to CSV format?');
  try {
    const response3 = await runCustomerSupportAgent(
      "How do I export my data to CSV format?",
      {
        userId: "user789",
        channel: "web",
        priority: "low"
      }
    );
    console.log(`Assistant: ${response3}\n`);
  } catch (error) {
    console.error(`Error: ${error.message}\n`);
  }

  // Test case 4: Complaint
  console.log('Test Case 4: Customer Complaint');
  console.log('User: Your service has been terrible lately and I\'m very frustrated');
  try {
    const response4 = await runCustomerSupportAgent(
      "Your service has been terrible lately and I'm very frustrated",
      {
        userId: "user101",
        customerId: "cust001",
        channel: "phone",
        priority: "urgent"
      }
    );
    console.log(`Assistant: ${response4}\n`);
  } catch (error) {
    console.error(`Error: ${error.message}\n`);
  }

  // Test case 5: Cancellation request
  console.log('Test Case 5: Cancellation Request');
  console.log('User: I want to cancel my subscription');
  try {
    const response5 = await runCustomerSupportAgent(
      "I want to cancel my subscription",
      {
        userId: "user202",
        customerId: "cust002",
        channel: "email",
        priority: "medium"
      }
    );
    console.log(`Assistant: ${response5}\n`);
  } catch (error) {
    console.error(`Error: ${error.message}\n`);
  }

  console.log('=== Testing Complete ===');
}

/**
 * Test the advanced workflow interface
 */
async function testAdvancedWorkflow(): Promise<void> {
  console.log('\n=== Testing Advanced Workflow Interface ===\n');

  // Create a custom inquiry
  const inquiry: CustomerInquiry = {
    id: "test-inquiry-001",
    message: "I need help setting up two-factor authentication on my account",
    timestamp: new Date(),
    channel: "chat",
    priority: "medium",
    customer_id: "cust001"
  };

  // Create the shared store
  const store = createCustomerSupportStore("test-user", inquiry, {
    sessionId: "test-session-001",
    maxResponseLength: 800,
    enableEscalation: true,
    knowledgeBaseEnabled: true
  });

  try {
    console.log('Running advanced workflow...');
    const result = await runCustomerSupportWorkflow(store);

    console.log('\n--- Workflow Results ---');
    console.log(`Ticket ID: ${result.support_ticket_id}`);
    console.log(`Intent: ${result.intent_classification?.intent} (${result.intent_classification?.confidence.toFixed(2)} confidence)`);
    console.log(`Urgency: ${result.intent_classification?.urgency_level}`);
    console.log(`Processing Time: ${result.total_processing_time}ms`);
    console.log(`Quality Score: ${result.response_validation?.quality_score}/10`);
    console.log(`Escalated: ${result.delivered_response?.escalated ? 'Yes' : 'No'}`);
    console.log(`\nFinal Response:\n${result.delivered_response?.final_response}`);

    if (result.knowledge_base_results) {
      console.log(`\nKnowledge Base Results:`);
      console.log(`- Articles: ${result.knowledge_base_results.articles.length}`);
      console.log(`- FAQs: ${result.knowledge_base_results.faqs.length}`);
    }

    if (result.response_validation?.issues && result.response_validation.issues.length > 0) {
      console.log(`\nValidation Issues:`);
      result.response_validation.issues.forEach(issue => console.log(`- ${issue}`));
    }

  } catch (error) {
    console.error(`Advanced workflow failed: ${error.message}`);
  }

  console.log('\n=== Advanced Testing Complete ===');
}

/**
 * Run all tests
 */
async function runAllTests(): Promise<void> {
  try {
    await testCustomerSupportAgent();
    await testAdvancedWorkflow();
  } catch (error) {
    console.error('Test execution failed:', error);
  }
}

// Export test functions for external use
export { testCustomerSupportAgent, testAdvancedWorkflow, runAllTests };

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
