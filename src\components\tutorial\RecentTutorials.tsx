import React from "react";
import { Link } from "react-router-dom";
import { RecentTutorial } from "@/types/tutorial";

const mockRecentTutorials: RecentTutorial[] = [
  {
    id: "1",
    title: "react-query",
    description: "A data fetching library for React applications",
    icon: "fa-brands fa-react",
    iconColor: "text-blue-500",
    chapters: 10,
    generatedAt: "2 hours ago",
    url: "/gallery",
  },
  {
    id: "2",
    title: "fastapi-tutorial",
    description: "Modern, fast web framework for building APIs with Python",
    icon: "fa-brands fa-python",
    iconColor: "text-green-600",
    chapters: 8,
    generatedAt: "yesterday",
    url: "/gallery",
  },
  {
    id: "3",
    title: "express-starter",
    description: "Minimal Express.js web application framework",
    icon: "fa-brands fa-node-js",
    iconColor: "text-yellow-600",
    chapters: 6,
    generatedAt: "3 days ago",
    url: "/gallery",
  },
];

export const RecentTutorials: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold mb-4 text-gray-800">
        Recently Generated
      </h2>

      <div className="space-y-4">
        {mockRecentTutorials.map((tutorial) => (
          <Link
            key={tutorial.id}
            to={tutorial.url}
            className="block border border-gray-200 rounded-md p-3 hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center mb-2">
              <i className={`${tutorial.icon} ${tutorial.iconColor} text-lg mr-2`}></i>
              <h3 className="font-medium text-gray-800">{tutorial.title}</h3>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {tutorial.description}
            </p>
            <div className="flex items-center text-xs text-gray-500">
              <span>{tutorial.chapters} chapters</span>
              <span className="mx-2">•</span>
              <span>Generated {tutorial.generatedAt}</span>
            </div>
          </Link>
        ))}
      </div>

      <Link
        to="/gallery"
        className="block text-center text-sm text-tutorial-primary hover:text-blue-700 mt-4"
      >
        View all generated tutorials →
      </Link>
    </div>
  );
};
