// src/Agents/CustomerSupport/nodes/GenerateResponse.ts

import { SharedStore, ResponseGeneration } from "../types";
import { Node } from '../../../pocketflow';
import { callLlm_openrouter } from "../../shared/callLlm_openrouter";
import { buildGenerateResponsePrompt } from "../prompts/generateResponse";
import { emitProgress, emitGraphStatus, emitSupportEvent } from "../utils/events";

/**
 * GenerateResponse Node
 * 
 * This node generates a customer support response based on:
 * - The customer inquiry
 * - Intent classification
 * - Retrieved context (customer info, knowledge base)
 * - Company policies and guidelines
 */
export class GenerateResponse extends Node<SharedStore> {
  
  constructor(maxRetries: number = 3, waitTime: number = 5) {
    super(maxRetries, waitTime);
  }

  async prep(shared: SharedStore): Promise<string> {
    emitGraphStatus("GenerateResponse", 0, "Preparing response generation");
    
    if (!shared.inquiry || !shared.intent_classification) {
      throw new Error("Missing inquiry or intent classification for response generation");
    }
    
    // Determine appropriate tone based on intent and urgency
    const suggestedTone = this.determineTone(
      shared.intent_classification.intent,
      shared.intent_classification.urgency_level
    );
    
    // Prepare context strings
    const customerContext = shared.customer_context ? 
      JSON.stringify(shared.customer_context, null, 2) : 
      "No customer context available";
    
    const knowledgeBaseResults = shared.knowledge_base_results ? 
      JSON.stringify(shared.knowledge_base_results, null, 2) : 
      "No knowledge base results available";
    
    const intentClassification = JSON.stringify(shared.intent_classification, null, 2);
    
    emitGraphStatus("GenerateResponse", 30, `Generating ${suggestedTone} response for ${shared.intent_classification.intent} inquiry`);
    
    const prompt = buildGenerateResponsePrompt(
      shared.inquiry.message,
      intentClassification,
      customerContext,
      knowledgeBaseResults,
      shared.intent_classification.urgency_level,
      shared.max_response_length,
      suggestedTone
    );
    
    return JSON.stringify({
      prompt,
      suggested_tone: suggestedTone,
      inquiry_id: shared.inquiry.id,
      ticket_id: shared.support_ticket_id
    });
  }

  async exec(args: string): Promise<string> {
    const parsedArgs = JSON.parse(args);
    
    emitGraphStatus("GenerateResponse", 40, "Calling LLM for response generation");
    
    try {
      const response = await callLlm_openrouter({
        prompt: parsedArgs.prompt,
        temperature: 0.3, // Moderate creativity for natural but consistent responses
        model: "google/gemini-2.5-flash-preview-05-20",
        use_cache: true,
        tutorial_id: parsedArgs.ticket_id || "unknown"
      });
      
      emitGraphStatus("GenerateResponse", 80, "Processing generated response");
      
      // Parse the JSON response from the LLM
      let responseData;
      try {
        // Extract JSON from the response if it's wrapped in markdown
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || 
                         response.match(/\{[\s\S]*\}/);
        
        if (jsonMatch) {
          responseData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } else {
          responseData = JSON.parse(response);
        }
      } catch (parseError) {
        console.warn("Failed to parse LLM response as JSON, creating fallback response");
        // Fallback response structure
        responseData = {
          response: response.trim(),
          tone: parsedArgs.suggested_tone,
          suggested_actions: [],
          escalation_needed: false,
          follow_up_required: false,
          reasoning: "Fallback response due to parsing error"
        };
      }
      
      // Validate and normalize the response data
      responseData = this.validateResponseData(responseData, parsedArgs.suggested_tone);
      
      emitGraphStatus("GenerateResponse", 90, "Response generated successfully");
      
      return JSON.stringify(responseData);
      
    } catch (error) {
      console.error("Error generating response:", error);
      throw new Error(`Response generation failed: ${error.message}`);
    }
  }

  async post(
    shared: SharedStore,
    _: string,
    execRes: string,
  ): Promise<string | undefined> {
    
    const responseData = JSON.parse(execRes);
    
    // Create the response generation object
    const responseGeneration: ResponseGeneration = {
      response: responseData.response,
      tone: responseData.tone,
      suggested_actions: responseData.suggested_actions || [],
      escalation_needed: responseData.escalation_needed || false,
      follow_up_required: responseData.follow_up_required || false
    };
    
    // Store in shared store
    shared.response_generation = responseGeneration;
    
    emitGraphStatus("GenerateResponse", 100, "Response generation completed");
    emitProgress("Inquiry Processing", 70, `Response generated (${responseGeneration.tone} tone, ${responseGeneration.response.length} chars)`);
    
    // Log response generation results
    emitSupportEvent("response_generated", {
      inquiry_id: shared.inquiry?.id,
      ticket_id: shared.support_ticket_id,
      response_length: responseGeneration.response.length,
      tone: responseGeneration.tone,
      escalation_needed: responseGeneration.escalation_needed,
      follow_up_required: responseGeneration.follow_up_required,
      suggested_actions_count: responseGeneration.suggested_actions.length
    });
    
    console.log(`✓ Response generated: ${responseGeneration.tone} tone, ${responseGeneration.response.length} characters`);
    
    return "default";
  }

  /**
   * Determine appropriate tone based on intent and urgency
   */
  private determineTone(intent: string, urgencyLevel: string): string {
    if (intent === 'complaint' || urgencyLevel === 'urgent') {
      return 'empathetic';
    } else if (intent === 'billing' || intent === 'cancellation') {
      return 'professional';
    } else if (intent === 'technical') {
      return 'technical';
    } else {
      return 'friendly';
    }
  }

  /**
   * Validate and normalize response data
   */
  private validateResponseData(data: any, suggestedTone: string): any {
    const validTones = ['professional', 'friendly', 'empathetic', 'technical'];
    
    return {
      response: data.response || "I apologize, but I'm having trouble generating a response. Please contact our support team directly.",
      tone: validTones.includes(data.tone) ? data.tone : suggestedTone,
      suggested_actions: Array.isArray(data.suggested_actions) ? data.suggested_actions : [],
      escalation_needed: Boolean(data.escalation_needed),
      follow_up_required: Boolean(data.follow_up_required),
      reasoning: data.reasoning || "Response generated successfully"
    };
  }
}
