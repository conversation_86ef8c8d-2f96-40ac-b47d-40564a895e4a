// src/Agents/CustomerSupport/nodes/ClassifyIntent.ts

import { SharedStore, IntentClassification } from "../types";
import { Node } from '../../../pocketflow';
import { callLlm_openrouter } from "../../shared/callLlm_openrouter";
import { buildClassifyIntentPrompt } from "../prompts/classifyIntent";
import { emitProgress, emitGraphStatus, emitSupportEvent } from "../utils/events";

/**
 * ClassifyIntent Node
 * 
 * This node analyzes the customer inquiry and classifies it into appropriate
 * categories (billing, technical, general, etc.) with confidence scores and urgency levels.
 */
export class ClassifyIntent extends Node<SharedStore> {
  
  constructor(maxRetries: number = 3, waitTime: number = 5) {
    super(maxRetries, waitTime);
  }

  async prep(shared: SharedStore): Promise<string> {
    emitGraphStatus("ClassifyIntent", 0, "Preparing intent classification");
    
    if (!shared.inquiry) {
      throw new Error("No inquiry found for classification");
    }
    
    // Prepare customer context for classification
    const customerContext = shared.customer_context ? 
      JSON.stringify(shared.customer_context) : 
      "No customer context available";
    
    emitGraphStatus("ClassifyIntent", 30, "Building classification prompt");
    
    const prompt = buildClassifyIntentPrompt(
      shared.inquiry.message,
      customerContext
    );
    
    return JSON.stringify({
      prompt,
      inquiry_id: shared.inquiry.id,
      ticket_id: shared.support_ticket_id
    });
  }

  async exec(args: string): Promise<string> {
    const parsedArgs = JSON.parse(args);
    
    emitGraphStatus("ClassifyIntent", 40, "Calling LLM for intent classification");
    
    try {
      const response = await callLlm_openrouter({
        prompt: parsedArgs.prompt,
        temperature: 0.1, // Low temperature for consistent classification
        model: "google/gemini-2.5-flash-preview-05-20",
        use_cache: true,
        tutorial_id: parsedArgs.ticket_id || "unknown"
      });
      
      emitGraphStatus("ClassifyIntent", 80, "Processing classification response");
      
      // Parse the JSON response from the LLM
      let classificationResult;
      try {
        // Extract JSON from the response if it's wrapped in markdown
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || 
                         response.match(/\{[\s\S]*\}/);
        
        if (jsonMatch) {
          classificationResult = JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } else {
          classificationResult = JSON.parse(response);
        }
      } catch (parseError) {
        console.warn("Failed to parse LLM response as JSON, using fallback classification");
        // Fallback classification
        classificationResult = {
          intent: "general",
          confidence: 0.5,
          sub_category: "unknown",
          urgency_level: "medium",
          reasoning: "Failed to parse LLM response"
        };
      }
      
      // Validate and normalize the classification result
      const validIntents = ['billing', 'technical', 'general', 'complaint', 'feature_request', 'cancellation'];
      const validUrgencyLevels = ['low', 'medium', 'high', 'urgent'];
      
      if (!validIntents.includes(classificationResult.intent)) {
        classificationResult.intent = 'general';
      }
      
      if (!validUrgencyLevels.includes(classificationResult.urgency_level)) {
        classificationResult.urgency_level = 'medium';
      }
      
      // Ensure confidence is between 0 and 1
      if (typeof classificationResult.confidence !== 'number' || 
          classificationResult.confidence < 0 || 
          classificationResult.confidence > 1) {
        classificationResult.confidence = 0.5;
      }
      
      emitGraphStatus("ClassifyIntent", 90, `Classified as: ${classificationResult.intent}`);
      
      return JSON.stringify(classificationResult);
      
    } catch (error) {
      console.error("Error in intent classification:", error);
      throw new Error(`Intent classification failed: ${error.message}`);
    }
  }

  async post(
    shared: SharedStore,
    _: string,
    execRes: string,
  ): Promise<string | undefined> {
    
    const classificationData = JSON.parse(execRes);
    
    // Create the intent classification object
    const intentClassification: IntentClassification = {
      intent: classificationData.intent,
      confidence: classificationData.confidence,
      sub_category: classificationData.sub_category,
      urgency_level: classificationData.urgency_level
    };
    
    // Store in shared store
    shared.intent_classification = intentClassification;
    
    emitGraphStatus("ClassifyIntent", 100, "Intent classification completed");
    emitProgress("Inquiry Processing", 30, `Classified as: ${intentClassification.intent} (${intentClassification.urgency_level} urgency)`);
    
    // Log classification results
    emitSupportEvent("intent_classified", {
      inquiry_id: shared.inquiry?.id,
      ticket_id: shared.support_ticket_id,
      intent: intentClassification.intent,
      confidence: intentClassification.confidence,
      urgency: intentClassification.urgency_level,
      sub_category: intentClassification.sub_category
    });
    
    console.log(`✓ Intent classified: ${intentClassification.intent} (confidence: ${intentClassification.confidence.toFixed(2)})`);
    
    return "default";
  }
}
