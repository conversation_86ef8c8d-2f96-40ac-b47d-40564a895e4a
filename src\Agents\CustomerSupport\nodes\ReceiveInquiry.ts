// src/Agents/CustomerSupport/nodes/ReceiveInquiry.ts

import { SharedStore, CustomerInquiry } from "../types";
import { Node } from '../../../pocketflow';
import { emitProgress, emitGraphStatus, emitSupportEvent } from "../utils/events";

/**
 * ReceiveInquiry Node
 * 
 * This node handles the initial receipt and processing of customer inquiries.
 * It validates the inquiry, assigns an ID, and prepares it for classification.
 */
export class ReceiveInquiry extends Node<SharedStore> {
  
  async prep(shared: SharedStore): Promise<string> {
    emitGraphStatus("ReceiveInquiry", 0, "Preparing to receive customer inquiry");
    
    // Record processing start time
    shared.processing_start_time = new Date();
    
    // Generate support ticket ID if not provided
    if (!shared.support_ticket_id) {
      shared.support_ticket_id = `ticket-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    emitGraphStatus("ReceiveInquiry", 20, "Generated support ticket ID");
    emitSupportEvent("ticket_created", { 
      ticket_id: shared.support_ticket_id,
      user_id: shared.user_id 
    });
    
    // Validate that we have an inquiry to process
    if (!shared.inquiry) {
      throw new Error("No customer inquiry provided");
    }
    
    emitGraphStatus("ReceiveInquiry", 50, "Validating inquiry data");
    
    return JSON.stringify({
      inquiry: shared.inquiry,
      ticket_id: shared.support_ticket_id,
      user_id: shared.user_id
    });
  }

  async exec(args: string): Promise<string> {
    const parsedArgs = JSON.parse(args);
    
    emitGraphStatus("ReceiveInquiry", 60, "Processing inquiry data");
    
    // Validate inquiry structure
    const inquiry = parsedArgs.inquiry;
    
    if (!inquiry.message || inquiry.message.trim().length === 0) {
      throw new Error("Inquiry message cannot be empty");
    }
    
    // Assign default values if missing
    const processedInquiry: CustomerInquiry = {
      id: inquiry.id || `inq-${Date.now()}`,
      message: inquiry.message.trim(),
      timestamp: inquiry.timestamp ? new Date(inquiry.timestamp) : new Date(),
      channel: inquiry.channel || 'web',
      priority: inquiry.priority || 'medium',
      customer_id: inquiry.customer_id || parsedArgs.user_id
    };
    
    emitGraphStatus("ReceiveInquiry", 80, "Inquiry processed and validated");
    
    // Log inquiry details for audit
    emitSupportEvent("inquiry_processed", {
      inquiry_id: processedInquiry.id,
      ticket_id: parsedArgs.ticket_id,
      channel: processedInquiry.channel,
      priority: processedInquiry.priority,
      message_length: processedInquiry.message.length
    });
    
    return JSON.stringify(processedInquiry);
  }

  async post(
    shared: SharedStore,
    _: string,
    execRes: string,
  ): Promise<string | undefined> {
    
    // Store the processed inquiry in shared store
    shared.inquiry = JSON.parse(execRes);
    
    emitGraphStatus("ReceiveInquiry", 100, "Inquiry received and stored successfully");
    emitProgress("Inquiry Processing", 10, `Received inquiry: ${shared.inquiry.id}`);
    
    // Log successful processing
    console.log(`✓ Inquiry received: ${shared.inquiry.id} - ${shared.inquiry.message.substring(0, 50)}...`);
    
    return "default";
  }
}
