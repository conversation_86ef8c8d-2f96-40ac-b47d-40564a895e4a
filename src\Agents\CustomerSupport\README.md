# Customer Support Agent

A comprehensive PocketFlow-based workflow for automated customer support using LLM-powered response generation.

## Overview

The Customer Support Agent automatically processes customer inquiries through a sophisticated pipeline that includes intent classification, context retrieval, response generation, quality validation, and delivery. It's designed to provide high-quality, contextually appropriate responses while maintaining the ability to escalate complex issues to human agents.

## Features

- **Automated Intent Classification**: Categorizes inquiries into billing, technical, general, complaint, feature request, or cancellation
- **Context-Aware Responses**: Leverages customer history and knowledge base for personalized responses
- **Quality Assurance**: Validates responses for appropriateness, accuracy, and professionalism
- **Multi-Channel Support**: Handles inquiries from email, chat, web, and phone channels
- **Escalation Management**: Automatically identifies when human intervention is needed
- **Comprehensive Logging**: Tracks all interactions for audit and improvement purposes

## Quick Start

### Simple Usage

```typescript
import { runCustomerSupportAgent } from './src/Agents/CustomerSupport';

// Basic usage
const response = await runCustomerSupportAgent(
  "I can't log into my account",
  {
    userId: "user123",
    channel: "chat",
    priority: "high"
  }
);

console.log(response);
```

### Advanced Usage

```typescript
import { 
  createCustomerSupportStore, 
  runCustomerSupportWorkflow,
  CustomerInquiry 
} from './src/Agents/CustomerSupport';

// Create a detailed inquiry
const inquiry: CustomerInquiry = {
  id: "inq-001",
  message: "I was charged twice this month and need a refund",
  timestamp: new Date(),
  channel: "email",
  priority: "medium",
  customer_id: "cust-456"
};

// Create shared store with configuration
const store = createCustomerSupportStore("user123", inquiry, {
  maxResponseLength: 800,
  enableEscalation: true,
  knowledgeBaseEnabled: true
});

// Run the complete workflow
const result = await runCustomerSupportWorkflow(store);

// Access detailed results
console.log("Intent:", result.intent_classification?.intent);
console.log("Quality Score:", result.response_validation?.quality_score);
console.log("Final Response:", result.delivered_response?.final_response);
```

## Architecture

### Workflow Pipeline

1. **ReceiveInquiry**: Validates and processes incoming customer inquiries
2. **ClassifyIntent**: Determines inquiry type and urgency using LLM analysis
3. **RetrieveContext**: Gathers customer information and relevant knowledge base content
4. **GenerateResponse**: Creates appropriate responses using context-aware prompts
5. **ValidateResponse**: Ensures response quality and appropriateness
6. **DeliverResponse**: Formats and delivers the final response with metrics

### Data Flow

```
Customer Inquiry → Intent Classification → Context Retrieval → 
Response Generation → Quality Validation → Response Delivery
```

## Configuration

### Environment Variables

- `OPENROUTER_API_KEY`: API key for OpenRouter LLM service (configured in shared utility)

### Workflow Options

```typescript
interface Options {
  userId?: string;                    // User identifier
  customerId?: string;               // Customer identifier for context lookup
  channel?: 'email' | 'chat' | 'phone' | 'web';  // Communication channel
  priority?: 'low' | 'medium' | 'high' | 'urgent';  // Inquiry priority
  maxResponseLength?: number;        // Maximum response length in characters
  enableEscalation?: boolean;        // Enable automatic escalation
  knowledgeBaseEnabled?: boolean;    // Enable knowledge base search
}
```

## Testing

Run the included test suite to verify functionality:

```typescript
import { runAllTests } from './src/Agents/CustomerSupport/test';

// Run all test scenarios
await runAllTests();
```

Test scenarios include:
- Technical issues (login problems)
- Billing inquiries (refund requests)
- General questions (how-to guides)
- Customer complaints
- Cancellation requests

## Knowledge Base

The system includes a mock knowledge base with:
- **Articles**: Detailed guides and troubleshooting information
- **FAQs**: Common questions and answers
- **Search**: Relevance-based content retrieval

### Adding Content

Extend the knowledge base by modifying `src/Agents/CustomerSupport/utils/knowledgeBase.ts`:

```typescript
const KNOWLEDGE_ARTICLES: KnowledgeArticle[] = [
  {
    id: 'kb001',
    title: 'Your Article Title',
    content: 'Detailed article content...',
    category: 'technical',
    tags: ['keyword1', 'keyword2']
  }
  // Add more articles...
];
```

## Monitoring and Analytics

The system provides comprehensive monitoring through event emission:

- **Progress Tracking**: Real-time workflow progress updates
- **Quality Metrics**: Response quality scores and processing times
- **Escalation Alerts**: Notifications for issues requiring human intervention
- **Usage Analytics**: Detailed logging for performance analysis

## Customization

### Custom Nodes

Create custom nodes by extending the base Node class:

```typescript
import { Node } from '../../../pocketflow';
import { SharedStore } from '../types';

class CustomNode extends Node<SharedStore> {
  async prep(shared: SharedStore): Promise<string> {
    // Preparation logic
    return "prepared data";
  }

  async exec(prepRes: string): Promise<string> {
    // Main execution logic
    return "execution result";
  }

  async post(shared: SharedStore, prepRes: string, execRes: string): Promise<string | undefined> {
    // Post-processing logic
    return "default";
  }
}
```

### Custom Flows

Build custom workflows by connecting nodes:

```typescript
import { Flow } from '../../../pocketflow';
import { ReceiveInquiry, CustomNode } from '../nodes';

function createCustomFlow(): Flow {
  const receive = new ReceiveInquiry();
  const custom = new CustomNode();
  
  receive.next(custom);
  
  return new Flow(receive);
}
```

## Performance Considerations

- **Caching**: LLM responses are cached to reduce API calls
- **Retry Logic**: Automatic retries with exponential backoff for failed requests
- **Parallel Processing**: Context retrieval operations run concurrently where possible
- **Token Optimization**: Efficient prompt engineering to minimize LLM token usage

## Security and Privacy

- **Data Protection**: No sensitive customer data is logged
- **API Security**: Secure API key management
- **Input Validation**: Comprehensive validation of all inputs
- **Error Handling**: Graceful error handling with fallback responses

## Future Enhancements

- **Multi-turn Conversations**: Support for ongoing dialogue
- **Sentiment Analysis**: Emotion detection for tone adaptation
- **Machine Learning**: Auto-improvement based on feedback
- **Integration APIs**: Connect to real support systems
- **Advanced Routing**: Specialized agent routing based on expertise

## Support

For questions or issues with the Customer Support Agent:

1. Check the test cases for usage examples
2. Review the design documentation in `docs/design.md`
3. Examine the individual node implementations for detailed behavior
4. Monitor the console output for debugging information
