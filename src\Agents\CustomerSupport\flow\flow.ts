// src/Agents/CustomerSupport/flow/flow.ts

import { Flow } from "../../../pocketflow";
import {
  ReceiveInquiry,
  ClassifyIntent,
  RetrieveContext,
  GenerateResponse,
  ValidateResponse,
  DeliverResponse,
} from "../nodes";

import { SharedStore } from "../types";

/**
 * Creates and returns the customer support workflow flow.
 * 
 * This flow processes customer inquiries through the following stages:
 * 1. ReceiveInquiry - Capture and validate the customer inquiry
 * 2. ClassifyIntent - Determine the type and urgency of the inquiry
 * 3. RetrieveContext - Get relevant customer and knowledge base information
 * 4. GenerateResponse - Create an appropriate response using LLM
 * 5. ValidateResponse - Ensure response quality and appropriateness
 * 6. DeliverResponse - Format and deliver the final response
 * 
 * @returns Flow instance configured for customer support
 */
export function create_customer_support_flow(): Flow {
  
  // Configuration for retry behavior
  const maxRetries = 3;
  const waitTime = 5; // seconds
  
  // Instantiate nodes with appropriate retry settings
  const receive_inquiry = new ReceiveInquiry();
  
  // Classification and context retrieval with moderate retry settings
  const classify_intent = new ClassifyIntent(maxRetries, waitTime);
  const retrieve_context = new RetrieveContext(2, 3); // Fewer retries for context retrieval
  
  // Response generation and validation with higher retry tolerance
  const generate_response = new GenerateResponse(maxRetries, waitTime);
  const validate_response = new ValidateResponse(2, 3); // Fewer retries for validation
  
  // Final delivery step
  const deliver_response = new DeliverResponse();

  // Connect nodes in sequence
  // Each node will proceed to the next on "default" action
  receive_inquiry.next(classify_intent);
  classify_intent.next(retrieve_context);
  retrieve_context.next(generate_response);
  generate_response.next(validate_response);
  validate_response.next(deliver_response);

  // Create flow starting with the first node
  const customer_support_flow = new Flow<SharedStore>(receive_inquiry);
  
  return customer_support_flow;
}

/**
 * Alternative flow creation with branching logic
 * This demonstrates how you could create more complex flows with conditional paths
 */
export function create_advanced_customer_support_flow(): Flow {
  
  const maxRetries = 3;
  const waitTime = 5;
  
  // Instantiate nodes
  const receive_inquiry = new ReceiveInquiry();
  const classify_intent = new ClassifyIntent(maxRetries, waitTime);
  const retrieve_context = new RetrieveContext(2, 3);
  const generate_response = new GenerateResponse(maxRetries, waitTime);
  const validate_response = new ValidateResponse(2, 3);
  const deliver_response = new DeliverResponse();
  
  // You could add additional nodes for specific scenarios
  // const escalate_to_human = new EscalateToHuman();
  // const regenerate_response = new RegenerateResponse();
  
  // Connect nodes with potential branching
  receive_inquiry.next(classify_intent);
  classify_intent.next(retrieve_context);
  retrieve_context.next(generate_response);
  generate_response.next(validate_response);
  
  // In a more advanced implementation, you could add conditional logic:
  // validate_response.on("regenerate", generate_response); // Loop back for regeneration
  // validate_response.on("escalate", escalate_to_human);   // Escalate to human
  // validate_response.on("default", deliver_response);     // Normal delivery
  
  // For now, use simple linear flow
  validate_response.next(deliver_response);

  const advanced_flow = new Flow<SharedStore>(receive_inquiry);
  
  return advanced_flow;
}
